# 十六进制字符串解码器

基于IDA Pro逆向分析的字符串解码算法Python实现。

## 功能概述

该模块实现了从IDA Pro分析的`decodeHexString`函数（地址：0x100085574）的解码算法，主要功能包括：

- 十六进制编码字符串的解码
- 使用位置相关的XOR密钥进行解密
- 支持中文和特殊字符
- 提供编码和解码的双向功能

## 算法原理

### 解码流程

1. **长度检查**：输入字符串长度必须大于66字符
2. **数据提取**：跳过前32字节和后32字节的填充数据
3. **十六进制转换**：将十六进制字符对转换为字节值
4. **XOR解密**：根据位置使用不同的密钥：
   - 4字节对齐位置（index % 4 == 0）：使用密钥 `0xB0`
   - 非对齐位置：使用密钥 `0xFB`
5. **字符串重建**：将解密后的字节转换为UTF-8字符串

### XOR密钥模式

```
位置:  0    1    2    3    4    5    6    7    ...
密钥: 0xB0 0xFB 0xFB 0xFB 0xB0 0xFB 0xFB 0xFB ...
```

## 使用方法

### 基本用法

```python
from hex_string_decoder import HexStringDecoder, decode_hex_string, encode_to_hex_string

# 创建解码器实例
decoder = HexStringDecoder()

# 编码字符串
original_text = "Hello, World! 你好世界！"
encoded_hex = decoder.encode_string(original_text)
print(f"编码后: {encoded_hex}")

# 解码字符串
decoded_text = decoder.decode_string(encoded_hex)
print(f"解码后: {decoded_text}")

# 使用便捷函数
encoded = encode_to_hex_string("测试文本")
decoded = decode_hex_string(encoded)
```

### 类方法

#### `HexStringDecoder.decode_string(input_string: str) -> Optional[str]`

解码十六进制编码的字符串。

**参数：**
- `input_string`: 十六进制编码的字符串

**返回：**
- 解码后的字符串，失败时返回 `None`

#### `HexStringDecoder.encode_string(input_string: str) -> Optional[str]`

将普通字符串编码为十六进制格式。

**参数：**
- `input_string`: 要编码的普通字符串

**返回：**
- 编码后的十六进制字符串，失败时返回 `None`

### 便捷函数

#### `decode_hex_string(hex_string: str) -> Optional[str]`

快速解码函数。

#### `encode_to_hex_string(plain_string: str) -> Optional[str]`

快速编码函数。

## 测试结果

运行 `python3 test_decoder.py` 查看完整测试结果：

```
=== 基本功能测试 ===
✅ 短字符串处理
✅ 英文字符串编码/解码
✅ 中文字符串编码/解码
✅ 混合字符串编码/解码
✅ 特殊字符编码/解码
✅ 数字字符串编码/解码

=== 边界情况测试 ===
✅ 空字符串处理
✅ None输入处理
✅ 非字符串输入处理
✅ 长度边界测试

=== XOR密钥测试 ===
✅ 密钥模式验证
✅ 位置对齐测试
```

## 文件结构

```
├── hex_string_decoder.py    # 主解码器模块
├── test_decoder.py          # 测试脚本
└── hex_decoder_README.md   # 说明文档
```

## 技术细节

### 原始函数分析

基于IDA Pro分析的原始函数特点：

- **函数地址**: 0x100085574
- **函数大小**: 0x62c字节
- **返回类型**: `__CFString *`
- **包含反调试代码**: 使用了复杂的控制流混淆

### 关键实现细节

1. **内存布局**: 编码后的数据包含64字节填充（前32+后32）
2. **字节序**: 使用小端字节序
3. **错误处理**: 对无效十六进制字符进行跳过处理
4. **字符编码**: 使用UTF-8编码处理中文字符

## 注意事项

1. 输入字符串长度必须大于66字符才会进行解码
2. 编码后的字符串包含填充数据，长度会显著增加
3. 解码过程中会自动忽略无效的十六进制字符
4. 支持中文和特殊字符，但需要确保UTF-8编码兼容性

## 许可证

本项目仅用于学习和研究目的。
