#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
十六进制字符串解码模块

基于IDA Pro分析的decodeHexString函数实现
地址: 0x100085574

该模块实现了与原始函数相同的解码算法：
1. 检查字符串长度（必须大于66字符）
2. 将十六进制字符串转换为字节
3. 使用位置相关的XOR密钥进行解密
4. 返回解码后的字符串
"""

import binascii
from typing import Optional


class HexStringDecoder:
    """十六进制字符串解码器"""
    
    # XOR解密密钥
    KEY_ALIGNED = 0xB0      # 4字节对齐位置使用的密钥
    KEY_UNALIGNED = 0xFB    # 非4字节对齐位置使用的密钥
    
    MIN_LENGTH = 66         # 最小字符串长度要求
    
    def __init__(self):
        """初始化解码器"""
        pass
    
    @staticmethod
    def decode_string(input_string: str) -> Optional[str]:
        """
        解码十六进制编码的字符串
        
        Args:
            input_string (str): 输入的十六进制字符串
            
        Returns:
            Optional[str]: 解码后的字符串，如果解码失败返回None
        """
        if not isinstance(input_string, str):
            return None
            
        # 检查字符串长度，必须大于66字符
        if len(input_string) <= HexStringDecoder.MIN_LENGTH:
            return ""
        
        try:
            # 将字符串转换为UTF-8字节
            data_bytes = input_string.encode('utf-8')
            data_length = len(data_bytes)
            
            # 计算解码后的数据长度（去掉前32字节和后32字节的填充）
            if data_length <= 64:  # 32 + 32
                return ""
                
            decode_length = data_length - 64
            decoded_bytes = bytearray(decode_length)
            
            # 从第32字节开始解码，每次处理2个字符（1个字节的十六进制表示）
            pos = 32  # 起始位置（跳过前32字节）
            output_index = 0
            
            while pos < data_length - 32 and output_index < decode_length:
                # 读取两个十六进制字符
                if pos + 1 >= len(data_bytes):
                    break
                    
                hex_char1 = chr(data_bytes[pos])
                hex_char2 = chr(data_bytes[pos + 1])
                hex_string = hex_char1 + hex_char2
                
                try:
                    # 将十六进制字符串转换为整数
                    byte_value = int(hex_string, 16)
                except ValueError:
                    # 如果不是有效的十六进制字符，跳过
                    pos += 2
                    continue
                
                # 根据输出位置选择XOR密钥
                if output_index % 4 == 0:
                    # 4字节对齐位置使用密钥0xB0
                    decoded_byte = byte_value ^ HexStringDecoder.KEY_ALIGNED
                else:
                    # 非4字节对齐位置使用密钥0xFB
                    decoded_byte = byte_value ^ HexStringDecoder.KEY_UNALIGNED
                
                decoded_bytes[output_index] = decoded_byte
                
                pos += 2
                output_index += 1
            
            # 将解码后的字节转换为字符串
            # 移除末尾的空字节
            while decoded_bytes and decoded_bytes[-1] == 0:
                decoded_bytes.pop()
                
            return decoded_bytes.decode('utf-8', errors='ignore')
            
        except Exception as e:
            print(f"解码过程中发生错误: {e}")
            return None
    
    @staticmethod
    def encode_string(input_string: str) -> Optional[str]:
        """
        编码字符串为十六进制格式（逆向操作）
        
        Args:
            input_string (str): 要编码的字符串
            
        Returns:
            Optional[str]: 编码后的十六进制字符串
        """
        if not isinstance(input_string, str):
            return None
            
        try:
            # 将字符串转换为UTF-8字节
            input_bytes = input_string.encode('utf-8')
            
            # 创建编码后的字节数组
            encoded_hex_chars = []
            
            # 添加前32字节的填充（可以是任意值，这里使用0）
            padding_front = "00" * 32
            encoded_hex_chars.append(padding_front)
            
            # 对每个字节进行XOR编码并转换为十六进制
            for i, byte_value in enumerate(input_bytes):
                if i % 4 == 0:
                    # 4字节对齐位置使用密钥0xB0
                    encoded_byte = byte_value ^ HexStringDecoder.KEY_ALIGNED
                else:
                    # 非4字节对齐位置使用密钥0xFB
                    encoded_byte = byte_value ^ HexStringDecoder.KEY_UNALIGNED
                
                # 转换为两位十六进制字符串
                hex_str = f"{encoded_byte:02x}"
                encoded_hex_chars.append(hex_str)
            
            # 添加后32字节的填充
            padding_back = "00" * 32
            encoded_hex_chars.append(padding_back)
            
            # 组合所有十六进制字符
            result = "".join(encoded_hex_chars)
            
            return result
            
        except Exception as e:
            print(f"编码过程中发生错误: {e}")
            return None


def decode_hex_string(hex_string: str) -> Optional[str]:
    """
    便捷函数：解码十六进制字符串
    
    Args:
        hex_string (str): 十六进制编码的字符串
        
    Returns:
        Optional[str]: 解码后的字符串
    """
    return HexStringDecoder.decode_string(hex_string)


def encode_to_hex_string(plain_string: str) -> Optional[str]:
    """
    便捷函数：将字符串编码为十六进制格式
    
    Args:
        plain_string (str): 要编码的普通字符串
        
    Returns:
        Optional[str]: 编码后的十六进制字符串
    """
    return HexStringDecoder.encode_string(plain_string)


if __name__ == "__main__":
    # 测试代码
    decoder = HexStringDecoder()
    
    print("=== 十六进制字符串解码器测试 ===")
    
    # # 测试1: 短字符串（应该返回空字符串）
    # short_string = "hello"
    # result1 = decoder.decode_string(short_string)
    # print(f"短字符串测试: '{short_string}' -> '{result1}'")
    
    # # 测试2: 编码和解码循环测试
    # test_string = "Hello, World! 这是一个测试字符串。"
    # print(f"\n原始字符串: '{test_string}'")
    
    # # 编码
    # encoded = decoder.encode_string(test_string)
    # if encoded:
    #     print(f"编码后: {encoded[:100]}..." if len(encoded) > 100 else f"编码后: {encoded}")
        
    #     # 解码
    #     decoded = decoder.decode_string(encoded)
    #     print(f"解码后: '{decoded}'")
        
    #     # 验证
    #     if decoded == test_string:
    #         print("✅ 编码/解码测试通过！")
    #     else:
    #         print("❌ 编码/解码测试失败！")
    # else:
    #     print("❌ 编码失败！")

    # test_string = "b95579b1e90eaad9c0334cb797db940c9dd6d6d69db9bebcf9b5dbabe5b9b7b2f3dbb0bee9d6d6d69dd6f1b6f9b2b9b2dabab5b9d7908a93db92bcc2c7cbb9bae1bebdbaf1b4b8bae1c3bab6f9b2b9b8d7b0b8bae1beba83f8b8ce88c5a3c39af8cb9ca2c2cdafade3bd89f1e1aa94b1f288b893dab98f9fe5cebdabc4cb9fbcd9a98cb0f6b797d4ff969a99e9a288cdd888b9bad8b89e9dfc8c9ac8e0ae8fcfc38f91c29fb7c3bdd2afabb8baafc2838095acad83b69dc2e497aa82c09fca89f7c3898ff597ca819fb7a2c880a8bac3ffc8888383b7949d809ca8bdc4ac9e81d595b790fdd4d09c89be8bb0e6f1958afd93ab9a8994d4bdfbbf9097deaca38cf98db8ccf2b3b990d6b996b587cb8fb487d0bcc2d6cecaceca8cc88ec9bfc8abfaa8bea2e0a3b79de8b381b3f490f1949baf8b82fa88c993e89582c980c293a1fad4b19ce18f8b93dacdabb988af9696e6c2b4b087b6b8b0d692ad95e58db194e6b3a3c2f39eb2bccacf968f878eaef1d3c8cac9c7b4adb7c6b6cbc9fcbc96cdc4ce81cbeaaea2909bbc8e81d5b0afa2f7929993c2a199cfde969cc2c091a98f9f839cb3c1d4d490fe9caf93e8a38299baa98cb2f4baaabaf2f1d6d69dd6d6befebfdbabe5b9b7b2f3dbb0bee9d6d6d69dd6f1c060378dc985254aed23c39af205c25b"
    test_string = "9bac3ae85daee4702c31941d5e388fe19f8b8992c69a8f9e9f8d9a899f969499d9979ed4fd9e9f92d1d4af94c59893a8c089928fd5ab9ed4d394959dd99cd48bc5999792d3d5909ec96e2fc9cc365d481a573d655131848ac2"
    decoded = decoder.decode_string(test_string)
    print(f"解码后: '{decoded}'")