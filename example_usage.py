#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
十六进制字符串解码器使用示例

演示如何使用hex_string_decoder模块进行字符串编码和解码
"""

from hex_string_decoder import HexStringDecoder, decode_hex_string, encode_to_hex_string


def demo_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")
    
    # 创建解码器实例
    decoder = HexStringDecoder()
    
    # 测试字符串
    original_text = "Hello, World! 你好，世界！"
    print(f"原始文本: {original_text}")
    
    # 编码
    encoded = decoder.encode_string(original_text)
    if encoded:
        print(f"编码后长度: {len(encoded)} 字符")
        print(f"编码后前100字符: {encoded[:100]}...")
        
        # 解码
        decoded = decoder.decode_string(encoded)
        if decoded:
            print(f"解码后文本: {decoded}")
            print(f"编码解码成功: {'✅' if decoded == original_text else '❌'}")
        else:
            print("❌ 解码失败")
    else:
        print("❌ 编码失败")
    print()


def demo_convenience_functions():
    """便捷函数使用示例"""
    print("=== 便捷函数使用示例 ===")
    
    test_text = "这是一个测试字符串 with English mixed!"
    print(f"测试文本: {test_text}")
    
    # 使用便捷函数编码
    encoded = encode_to_hex_string(test_text)
    if encoded:
        print(f"编码成功，长度: {len(encoded)}")
        
        # 使用便捷函数解码
        decoded = decode_hex_string(encoded)
        if decoded:
            print(f"解码结果: {decoded}")
            print(f"结果正确: {'✅' if decoded == test_text else '❌'}")
        else:
            print("❌ 解码失败")
    else:
        print("❌ 编码失败")
    print()


def demo_edge_cases():
    """边界情况示例"""
    print("=== 边界情况示例 ===")
    
    decoder = HexStringDecoder()
    
    # 测试短字符串
    short_text = "short"
    print(f"短字符串测试: '{short_text}'")
    result = decoder.decode_string(short_text)
    print(f"结果: '{result}' (应该为空字符串)")
    
    # 测试空字符串
    empty_text = ""
    print(f"空字符串测试: '{empty_text}'")
    result = decoder.decode_string(empty_text)
    print(f"结果: '{result}' (应该为空字符串)")
    
    # 测试None
    print("None输入测试:")
    result = decoder.decode_string(None)
    print(f"结果: {result} (应该为None)")
    print()


def demo_special_characters():
    """特殊字符处理示例"""
    print("=== 特殊字符处理示例 ===")
    
    decoder = HexStringDecoder()
    
    special_texts = [
        "特殊符号: !@#$%^&*()",
        "数字: 1234567890",
        "中文标点：，。！？；：",
        "Emoji: 😀😃😄😁",
        "混合: ABC中文123!@#"
    ]
    
    for i, text in enumerate(special_texts, 1):
        print(f"测试 {i}: {text}")
        
        encoded = decoder.encode_string(text)
        if encoded:
            decoded = decoder.decode_string(encoded)
            success = decoded == text
            print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
            if not success:
                print(f"   原始: {text}")
                print(f"   解码: {decoded}")
        else:
            print("   ❌ 编码失败")
        print()


def demo_manual_hex_analysis():
    """手动十六进制分析示例"""
    print("=== 手动十六进制分析示例 ===")
    
    decoder = HexStringDecoder()
    
    # 使用简单的ASCII字符进行分析
    test_text = "ABCD"
    print(f"分析文本: '{test_text}'")
    
    # 显示原始字节
    original_bytes = test_text.encode('utf-8')
    print("原始字节:")
    for i, byte_val in enumerate(original_bytes):
        print(f"   位置 {i}: 0x{byte_val:02X} ('{chr(byte_val)}')")
    
    # 编码
    encoded = decoder.encode_string(test_text)
    if encoded:
        print(f"\n编码后长度: {len(encoded)}")
        print(f"编码后内容: {encoded}")
        
        # 分析编码结构
        print("\n编码结构分析:")
        print(f"前32字节填充: {encoded[:64]}")
        print(f"实际数据部分: {encoded[64:-64]}")
        print(f"后32字节填充: {encoded[-64:]}")
        
        # 解码验证
        decoded = decoder.decode_string(encoded)
        print(f"\n解码结果: '{decoded}'")
        print(f"验证成功: {'✅' if decoded == test_text else '❌'}")
    else:
        print("❌ 编码失败")
    print()


def main():
    """主演示函数"""
    print("十六进制字符串解码器 - 使用示例")
    print("=" * 50)
    
    try:
        demo_basic_usage()
        demo_convenience_functions()
        demo_edge_cases()
        demo_special_characters()
        demo_manual_hex_analysis()
        
        print("=" * 50)
        print("所有示例演示完成！")
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
