#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
十六进制字符串解码器测试脚本

测试hex_string_decoder模块的各种功能
"""

import sys
import os
from hex_string_decoder import HexStringDecoder, decode_hex_string, encode_to_hex_string


def test_basic_functionality():
    """测试基本功能"""
    print("=== 基本功能测试 ===")
    
    decoder = HexStringDecoder()
    
    # 测试1: 短字符串
    print("1. 短字符串测试:")
    short_string = "short"
    result = decoder.decode_string(short_string)
    print(f"   输入: '{short_string}' (长度: {len(short_string)})")
    print(f"   输出: '{result}'")
    print(f"   预期: 空字符串 - {'✅ 通过' if result == '' else '❌ 失败'}")
    print()
    
    # 测试2: 编码解码循环
    print("2. 编码解码循环测试:")
    test_strings = [
        "Hello World",
        "测试中文字符",
        "Mixed 混合 text 文本",
        "Special chars: !@#$%^&*()",
        "Numbers: 1234567890"
    ]
    
    for i, test_str in enumerate(test_strings, 1):
        print(f"   测试 {i}: '{test_str}'")
        
        # 编码
        encoded = decoder.encode_string(test_str)
        if encoded is None:
            print(f"      ❌ 编码失败")
            continue
            
        print(f"      编码长度: {len(encoded)}")
        
        # 解码
        decoded = decoder.decode_string(encoded)
        if decoded is None:
            print(f"      ❌ 解码失败")
            continue
            
        # 验证
        success = decoded == test_str
        print(f"      解码结果: '{decoded}'")
        print(f"      {'✅ 通过' if success else '❌ 失败'}")
        print()


def test_edge_cases():
    """测试边界情况"""
    print("=== 边界情况测试 ===")
    
    decoder = HexStringDecoder()
    
    # 测试1: 空字符串
    print("1. 空字符串测试:")
    result = decoder.decode_string("")
    print(f"   结果: '{result}' - {'✅ 通过' if result == '' else '❌ 失败'}")
    
    # 测试2: None输入
    print("2. None输入测试:")
    result = decoder.decode_string(None)
    print(f"   结果: {result} - {'✅ 通过' if result is None else '❌ 失败'}")
    
    # 测试3: 非字符串输入
    print("3. 非字符串输入测试:")
    result = decoder.decode_string(123)
    print(f"   结果: {result} - {'✅ 通过' if result is None else '❌ 失败'}")
    
    # 测试4: 刚好66字符的字符串
    print("4. 66字符边界测试:")
    boundary_string = "a" * 66
    result = decoder.decode_string(boundary_string)
    print(f"   输入长度: {len(boundary_string)}")
    print(f"   结果: '{result}' - {'✅ 通过' if result == '' else '❌ 失败'}")
    
    # 测试5: 67字符的字符串
    print("5. 67字符测试:")
    over_boundary = "a" * 67
    encoded = decoder.encode_string("test")
    if encoded and len(encoded) >= 67:
        result = decoder.decode_string(encoded)
        print(f"   使用编码后的字符串进行测试")
        print(f"   编码长度: {len(encoded)}")
        print(f"   解码成功: {'✅ 是' if result is not None else '❌ 否'}")
    print()


def test_convenience_functions():
    """测试便捷函数"""
    print("=== 便捷函数测试 ===")
    
    test_string = "便捷函数测试"
    print(f"测试字符串: '{test_string}'")
    
    # 使用便捷函数编码
    encoded = encode_to_hex_string(test_string)
    print(f"编码结果长度: {len(encoded) if encoded else 'None'}")
    
    # 使用便捷函数解码
    if encoded:
        decoded = decode_hex_string(encoded)
        print(f"解码结果: '{decoded}'")
        success = decoded == test_string
        print(f"测试结果: {'✅ 通过' if success else '❌ 失败'}")
    else:
        print("❌ 编码失败")
    print()


def test_xor_keys():
    """测试XOR密钥的正确性"""
    print("=== XOR密钥测试 ===")
    
    # 创建一个测试字符串，确保能测试到不同的对齐位置
    test_data = "ABCDEFGH"  # 8个字符，可以测试0,1,2,3,0,1,2,3的对齐模式
    
    decoder = HexStringDecoder()
    encoded = decoder.encode_string(test_data)
    
    if encoded:
        print(f"原始数据: {test_data}")
        print(f"编码长度: {len(encoded)}")
        
        # 手动验证XOR密钥
        data_bytes = test_data.encode('utf-8')
        print("XOR密钥验证:")
        
        for i, byte_val in enumerate(data_bytes):
            if i % 4 == 0:
                expected_key = HexStringDecoder.KEY_ALIGNED
                key_name = "ALIGNED(0xB0)"
            else:
                expected_key = HexStringDecoder.KEY_UNALIGNED
                key_name = "UNALIGNED(0xFB)"
            
            encoded_val = byte_val ^ expected_key
            print(f"   位置{i}: 0x{byte_val:02X} ^ 0x{expected_key:02X} = 0x{encoded_val:02X} ({key_name})")
        
        # 验证解码
        decoded = decoder.decode_string(encoded)
        success = decoded == test_data
        print(f"解码验证: {'✅ 通过' if success else '❌ 失败'}")
    else:
        print("❌ 编码失败")
    print()


def main():
    """主测试函数"""
    print("十六进制字符串解码器 - 完整测试套件")
    print("=" * 50)
    
    try:
        test_basic_functionality()
        test_edge_cases()
        test_convenience_functions()
        test_xor_keys()
        
        print("=" * 50)
        print("所有测试完成！")
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
